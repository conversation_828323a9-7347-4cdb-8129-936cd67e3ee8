import React, { useState } from 'react';
import { Input, Button } from 'antd';
import {
  SearchOutlined,
  CaretDownOutlined,
  SoundOutlined,
  FolderOutlined,
  WalletOutlined,
  ShoppingOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';

// 模拟音乐数据
const mockMusicData = [
  {
    id: 1,
    title: 'Summer Vibes',
    artist: 'DJ <PERSON>',
    genre: 'Pop',
    revenue: '22,345.60',
    streams: '1.2M',
    cover: '/api/placeholder/200/200',
  },
  {
    id: 2,
    title: 'Jazz Night',
    artist: '<PERSON>',
    genre: 'Jazz',
    revenue: '9,345.30',
    streams: '856K',
    cover: '/api/placeholder/200/200',
  },
  {
    id: 3,
    title: 'Peaceful Mind',
    artist: 'Meditation Masters',
    genre: 'Instrumental Music',
    revenue: '8,345.61',
    streams: '743K',
    cover: '/api/placeholder/200/200',
  },
  {
    id: 4,
    title: 'City Lights',
    artist: 'Urban Beats',
    genre: 'Pop',
    revenue: '8,041.70',
    streams: '692K',
    cover: '/api/placeholder/200/200',
  },
  {
    id: 5,
    title: 'Smooth Jazz',
    artist: 'The Jazz Collective',
    genre: 'Jazz',
    revenue: '7,441.23',
    streams: '634K',
    cover: '/api/placeholder/200/200',
  },
  {
    id: 6,
    title: 'Electronic Dreams',
    artist: 'Synth Wave',
    genre: 'Pop',
    revenue: '5,441.58',
    streams: '521K',
    cover: '/api/placeholder/200/200',
  },
  {
    id: 7,
    title: 'Acoustic Soul',
    artist: 'Guitar Hero',
    genre: 'Jazz',
    revenue: '5,449.36',
    streams: '498K',
    cover: '/api/placeholder/200/200',
  },
  {
    id: 8,
    title: 'Morning Coffee',
    artist: 'Cafe Sounds',
    genre: 'Pop',
    revenue: '6,442.14',
    streams: '567K',
    cover: '/api/placeholder/200/200',
  },
];

const getNavigationItems = (t: any) => [
  {
    key: 'music-market',
    label: t('common.navigation.musicMarket'),
    url: '/music-market',
    active: true,
    icon: SoundOutlined,
  },
  {
    key: 'my-assets',
    label: t('common.navigation.myAssets'),
    url: '/my-assets',
    active: false,
    icon: FolderOutlined,
  },
  {
    key: 'my-balance',
    label: t('common.navigation.myBalance'),
    url: '/my-balance',
    active: false,
    icon: WalletOutlined,
  },
  {
    key: 'my-orders',
    label: t('common.navigation.myOrders'),
    url: '/my-orders',
    active: false,
    icon: ShoppingOutlined,
  },
  {
    key: 'submit-music',
    label: t('common.navigation.submitMusic'),
    url: '/submit-music',
    active: false,
    icon: UploadOutlined,
  },
];

const MusicMarket: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const [searchValue, setSearchValue] = useState('');

  const navigationItems = getNavigationItems(t);

  const handleNavigate = (url: string) => {
    navigate(url);
  };

  return (
    <div className="bg-black min-h-screen w-full">
      {/* 主要内容区域 */}
      <div className="bg-[#0d0d0d] min-h-screen flex">
        {/* 左侧导航 */}
        <div className="w-[307px] pt-8 px-4">
          <div className="space-y-2">
            {navigationItems.map(item => (
              <div
                key={item.key}
                className={`
                  relative px-6 py-3 rounded-md cursor-pointer transition-colors
                  ${
                    item.active
                      ? 'text-primary border-2 border-primary border-solid bg-[#1a1a1a]'
                      : 'text-[#999999] hover:text-white hover:bg-[#1a1a1a]'
                  }
                `}
                onClick={() => handleNavigate(item.url)}
              >
                <div className="flex items-center">
                  {/* 图标 */}
                  <item.icon className="w-6 h-6 mr-3" />
                  <span className="text-18px font-bold">{item.label}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧内容区域 */}
        <div className="flex-1 p-8">
          {/* 搜索栏 */}
          <div className="mb-8">
            <div className="relative w-[400px]">
              <Input
                placeholder={t('musicMarket.searchPlaceholder')}
                value={searchValue}
                onChange={e => setSearchValue(e.target.value)}
                className="h-12 pl-4 pr-12 bg-[#1a1a1a] border-[#333] text-white placeholder:text-[#666] rounded-lg"
                prefix={<SearchOutlined className="text-[#666] mr-2" />}
              />
            </div>
          </div>

          {/* 音乐网格 */}
          <div className="grid grid-cols-4 gap-4 mb-12">
            {mockMusicData.map(music => (
              <div
                key={music.id}
                className="bg-transparent rounded-lg overflow-hidden hover:opacity-80 transition-opacity cursor-pointer"
              >
                <div className="aspect-square bg-[#333] relative rounded-lg overflow-hidden">
                  <img
                    src={music.cover}
                    alt={music.title}
                    className="w-full h-full object-cover"
                    onError={e => {
                      (e.target as HTMLImageElement).src =
                        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkb21pbmFudC1iYXNlbGluZT0iY2VudHJhbCIgZmlsbD0iIzY2NiIgZm9udC1zaXplPSIxNCIgZm9udC1mYW1pbHk9IkFyaWFsIj5NdXNpYyBDb3ZlcjwvdGV4dD4KPHN2Zz4=';
                    }}
                  />
                </div>
                <div className="pt-3">
                  <h3 className="text-white font-medium text-14px mb-1 truncate">
                    {music.title}
                  </h3>
                  <p className="text-[#999] text-12px truncate">
                    {music.artist}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* 表格视图 */}
          <div className="bg-[#1a1a1a] rounded-lg overflow-hidden">
            {/* 表格头部 */}
            <div className="grid grid-cols-4 gap-4 p-4 border-b border-[#333] bg-[#222]">
              <div className="flex items-center text-[#999] text-12px font-semibold">
                <span>{t('musicMarket.table.title')}</span>
              </div>
              <div className="flex items-center text-[#999] text-12px font-semibold">
                <span>{t('musicMarket.table.genre')}</span>
                <CaretDownOutlined className="ml-1 text-10px" />
              </div>
              <div className="flex items-center text-[#999] text-12px font-semibold">
                <span>{t('musicMarket.table.revenue')}</span>
                <CaretDownOutlined className="ml-1 text-10px" />
              </div>
              <div className="flex items-center text-[#999] text-12px font-semibold">
                <span>{t('musicMarket.table.streams')}</span>
                <CaretDownOutlined className="ml-1 text-10px" />
              </div>
            </div>

            {/* 表格内容 */}
            <div className="divide-y divide-[#333]">
              {mockMusicData.map(music => (
                <div
                  key={music.id}
                  className="grid grid-cols-4 gap-4 p-4 hover:bg-[#222] transition-colors"
                >
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-[#333] rounded mr-3 flex-shrink-0">
                      <img
                        src={music.cover}
                        alt={music.title}
                        className="w-full h-full object-cover rounded"
                        onError={e => {
                          (e.target as HTMLImageElement).src =
                            'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjI0IiB5PSIyNCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9ImNlbnRyYWwiIGZpbGw9IiM2NjYiIGZvbnQtc2l6ZT0iMTAiIGZvbnQtZmFtaWx5PSJBcmlhbCI+4pmqPC90ZXh0Pgo8L3N2Zz4=';
                        }}
                      />
                    </div>
                    <div>
                      <div className="text-white text-14px font-medium">
                        {music.title}
                      </div>
                      <div className="text-[#999] text-12px">
                        {music.artist}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center text-[#656565] text-14px">
                    {music.genre}
                  </div>
                  <div className="flex items-center text-[#656565] text-14px">
                    {music.revenue}
                  </div>
                  <div className="flex items-center text-[#656565] text-14px">
                    {music.streams}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MusicMarket;
